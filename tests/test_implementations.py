"""
Test suite for the completed MCP implementations.

This module tests the newly implemented reservation and guest tools.
"""

import asyncio
import pytest
from unittest.mock import AsyncMock, patch
from datetime import datetime, timedelta

from src.tools.reservations import create_reservation, update_reservation
from src.tools.guests import create_guest, update_guest
from src.cloudbeds_client import api_request


class TestReservationTools:
    """Test cases for reservation tools."""

    @pytest.mark.asyncio
    async def test_create_reservation_success(self):
        """Test successful reservation creation."""
        reservation_data = {
            "startDate": "2024-03-01",
            "endDate": "2024-03-05",
            "guestFirstName": "John",
            "guestLastName": "Doe",
            "guestEmail": "<EMAIL>",
            "adults": 2,
            "roomTypeID": "123"
        }
        
        with patch('src.tools.reservations.api_request') as mock_api:
            mock_api.return_value = {"reservationID": "12345", "status": "confirmed"}
            
            result = await create_reservation(reservation_data)
            
            assert "reservationID" in result
            mock_api.assert_called_once_with(
                "postReservation", 
                data=reservation_data, 
                property_id=None, 
                method="POST"
            )

    @pytest.mark.asyncio
    async def test_create_reservation_missing_fields(self):
        """Test reservation creation with missing required fields."""
        reservation_data = {
            "startDate": "2024-03-01",
            "endDate": "2024-03-05"
            # Missing required fields
        }
        
        result = await create_reservation(reservation_data)
        
        assert "error" in result
        assert "Missing required fields" in result["error"]

    @pytest.mark.asyncio
    async def test_create_reservation_invalid_dates(self):
        """Test reservation creation with invalid dates."""
        reservation_data = {
            "startDate": "2024-03-05",
            "endDate": "2024-03-01",  # End before start
            "guestFirstName": "John",
            "guestLastName": "Doe",
            "guestEmail": "<EMAIL>",
            "adults": 2,
            "roomTypeID": "123"
        }
        
        result = await create_reservation(reservation_data)
        
        assert "error" in result
        assert "Check-in date must be before check-out date" in result["error"]

    @pytest.mark.asyncio
    async def test_update_reservation_success(self):
        """Test successful reservation update."""
        update_data = {
            "adults": 3,
            "notes": "Updated reservation"
        }
        
        with patch('src.tools.reservations.api_request') as mock_api:
            mock_api.return_value = {"reservationID": "12345", "status": "updated"}
            
            result = await update_reservation("12345", update_data)
            
            assert "reservationID" in result
            expected_data = update_data.copy()
            expected_data["reservationID"] = "12345"
            mock_api.assert_called_once_with(
                "putReservation", 
                data=expected_data, 
                property_id=None, 
                method="PUT"
            )

    @pytest.mark.asyncio
    async def test_update_reservation_missing_id(self):
        """Test reservation update with missing ID."""
        result = await update_reservation("", {"adults": 3})
        
        assert "error" in result
        assert "Reservation ID is required" in result["error"]


class TestGuestTools:
    """Test cases for guest tools."""

    @pytest.mark.asyncio
    async def test_create_guest_success(self):
        """Test successful guest creation."""
        guest_data = {
            "guestFirstName": "Jane",
            "guestLastName": "Smith",
            "guestEmail": "<EMAIL>",
            "guestPhone": "+1234567890"
        }
        
        with patch('src.tools.guests.api_request') as mock_api:
            mock_api.return_value = {"guestID": "67890", "status": "created"}
            
            result = await create_guest(guest_data)
            
            assert "guestID" in result
            mock_api.assert_called_once_with(
                "postGuest", 
                data=guest_data, 
                property_id=None, 
                method="POST"
            )

    @pytest.mark.asyncio
    async def test_create_guest_missing_fields(self):
        """Test guest creation with missing required fields."""
        guest_data = {
            "guestFirstName": "Jane"
            # Missing required fields
        }
        
        result = await create_guest(guest_data)
        
        assert "error" in result
        assert "Missing required fields" in result["error"]

    @pytest.mark.asyncio
    async def test_create_guest_invalid_email(self):
        """Test guest creation with invalid email."""
        guest_data = {
            "guestFirstName": "Jane",
            "guestLastName": "Smith",
            "guestEmail": "invalid-email"
        }
        
        result = await create_guest(guest_data)
        
        assert "error" in result
        assert "Invalid email format" in result["error"]

    @pytest.mark.asyncio
    async def test_update_guest_success(self):
        """Test successful guest update."""
        update_data = {
            "guestPhone": "+0987654321",
            "guestCity": "New York"
        }
        
        with patch('src.tools.guests.api_request') as mock_api:
            mock_api.return_value = {"guestID": "67890", "status": "updated"}
            
            result = await update_guest("67890", update_data)
            
            assert "guestID" in result
            expected_data = update_data.copy()
            expected_data["guestID"] = "67890"
            mock_api.assert_called_once_with(
                "putGuest", 
                data=expected_data, 
                property_id=None, 
                method="PUT"
            )

    @pytest.mark.asyncio
    async def test_update_guest_missing_id(self):
        """Test guest update with missing ID."""
        result = await update_guest("", {"guestPhone": "+1234567890"})
        
        assert "error" in result
        assert "Guest ID is required" in result["error"]


class TestAPIClient:
    """Test cases for the enhanced API client."""

    @pytest.mark.asyncio
    async def test_api_request_post(self):
        """Test POST request functionality."""
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = AsyncMock()
            mock_response.status_code = 201
            mock_response.json.return_value = {"success": True, "data": {"id": "123"}}
            
            mock_client.return_value.__aenter__.return_value.post.return_value = mock_response
            
            result = await api_request("postReservation", method="POST", data={"test": "data"})
            
            assert result == {"id": "123"}

    @pytest.mark.asyncio
    async def test_api_request_put(self):
        """Test PUT request functionality."""
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = AsyncMock()
            mock_response.status_code = 200
            mock_response.json.return_value = {"success": True, "data": {"updated": True}}
            
            mock_client.return_value.__aenter__.return_value.put.return_value = mock_response
            
            result = await api_request("putReservation", method="PUT", data={"test": "data"})
            
            assert result == {"updated": True}

    @pytest.mark.asyncio
    async def test_api_request_error_handling(self):
        """Test error handling in API requests."""
        with patch('httpx.AsyncClient') as mock_client:
            mock_response = AsyncMock()
            mock_response.status_code = 400
            mock_response.text = "Bad Request"
            
            mock_client.return_value.__aenter__.return_value.post.return_value = mock_response
            
            result = await api_request("postReservation", method="POST", data={"test": "data"})
            
            assert "error" in result
            assert "HTTP 400" in result["error"]


if __name__ == "__main__":
    # Run tests
    pytest.main([__file__, "-v"])
