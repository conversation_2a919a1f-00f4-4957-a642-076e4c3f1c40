#!/usr/bin/env python3
"""
Debug script to diagnose property access issues with Cloudbeds API.
"""

import asyncio
import sys
import os
import json
sys.path.append('.')

from src.cloudbeds_client import api_request
from src.config import CLOUDBEDS_API_KEY, CLOUDBEDS_PROPERTY_ID

async def debug_property_access():
    """Debug property access for different request types."""

    print("🔍 Debugging Cloudbeds API Property Access")
    print("=" * 60)

    print(f"API Key: {CLOUDBEDS_API_KEY[:10]}..." if CLOUDBEDS_API_KEY else "❌ No API Key")
    print(f"Property ID: {CLOUDBEDS_PROPERTY_ID}")
    print()

    # Test 1: GET request that should work (existing functionality)
    print("1. Testing GET request (should work)...")
    try:
        result = await api_request("getRoomTypes", property_id=CLOUDBEDS_PROPERTY_ID)
        if isinstance(result, list) and len(result) > 0:
            print("   ✅ GET request successful - API key and property ID are valid")
        elif "error" in result:
            print(f"   ❌ GET request failed: {result['error']}")
        else:
            print(f"   ⚠️  GET request returned: {result}")
    except Exception as e:
        print(f"   ❌ GET request exception: {e}")

    # Test 2: POST request with property ID in body
    print("\n2. Testing POST request with property ID in body...")
    test_data = {
        "propertyID": CLOUDBEDS_PROPERTY_ID,
        "guestFirstName": "Test",
        "guestLastName": "Debug",
        "guestEmail": "<EMAIL>"
    }

    try:
        result = await api_request("postGuest", method="POST", data=test_data)
        print(f"   Result: {result}")

        if "error" in result:
            if "You don't have access to property ID" in result["error"]:
                print("   ❌ Property ID access error (this is the issue we're trying to fix)")
            elif "property" in result["error"].lower():
                print("   ❌ Property-related error")
            else:
                print(f"   ⚠️  Different error: {result['error']}")
        else:
            print("   ✅ POST request successful!")
    except Exception as e:
        print(f"   ❌ POST request exception: {e}")

    # Test 3: POST request with property ID in URL params (old way)
    print("\n3. Testing POST request with property ID in URL params...")
    test_data_no_prop = {
        "guestFirstName": "Test",
        "guestLastName": "Debug",
        "guestEmail": "<EMAIL>"
    }

    try:
        # Use a custom API call that forces property ID in URL params
        import httpx
        from src.config import API_BASE_URL, API_RATE_LIMIT
        from src.cloudbeds_client import get_access_token

        url = f"{API_BASE_URL}/postGuest"
        headers = {
            "Authorization": f"Bearer {get_access_token()}",
            "Content-Type": "application/json"
        }
        params = {"propertyID": CLOUDBEDS_PROPERTY_ID}

        async with httpx.AsyncClient() as client:
            response = await client.post(url, json=test_data_no_prop, headers=headers, params=params)

        if response.status_code in [200, 201, 202, 204]:
            try:
                data = response.json()
                if data.get('success'):
                    result = data.get('data', {})
                    print("   ✅ POST with URL params successful!")
                else:
                    result = {"error": data.get('message', 'Unknown error')}
                    print(f"   ❌ POST failed: {result['error']}")
            except ValueError:
                result = {"success": True}
                print("   ✅ POST with URL params successful (empty response)!")
        else:
            result = {"error": f"HTTP {response.status_code}: {response.text}"}
            print(f"   ❌ POST failed: {result['error']}")

    except Exception as e:
        print(f"   ❌ POST request exception: {e}")

    # Test 4: Check what happens with no property ID at all
    print("\n4. Testing POST request with no property ID...")
    test_data_minimal = {
        "guestFirstName": "Test",
        "guestLastName": "Debug",
        "guestEmail": "<EMAIL>"
    }

    try:
        result = await api_request("postGuest", method="POST", data=test_data_minimal)
        print(f"   Result: {result}")
    except Exception as e:
        print(f"   ❌ POST request exception: {e}")

    # Test 5: Check API permissions by trying to get property info
    print("\n5. Testing property info access...")
    try:
        result = await api_request("getProperty", property_id=CLOUDBEDS_PROPERTY_ID)
        if "error" in result:
            print(f"   ❌ Cannot access property info: {result['error']}")
        else:
            print("   ✅ Can access property info")
            if isinstance(result, dict) and "propertyName" in result:
                print(f"   Property Name: {result.get('propertyName', 'Unknown')}")
    except Exception as e:
        print(f"   ❌ Property info exception: {e}")

    print("\n" + "=" * 60)
    print("🏁 Debug completed!")
    print("\nNext steps based on results:")
    print("- If GET works but POST fails: API key may not have write permissions")
    print("- If property info fails: Property ID may be incorrect")
    print("- If all fail: API key or authentication issue")

if __name__ == "__main__":
    if not CLOUDBEDS_API_KEY:
        print("❌ CLOUDBEDS_API_KEY environment variable not set")
        sys.exit(1)

    if not CLOUDBEDS_PROPERTY_ID:
        print("❌ CLOUDBEDS_PROPERTY_ID environment variable not set")
        sys.exit(1)

    asyncio.run(debug_property_access())
