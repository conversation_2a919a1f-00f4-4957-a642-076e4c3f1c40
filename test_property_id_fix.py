#!/usr/bin/env python3
"""
Test script to verify the property ID fix for write operations.
"""

import asyncio
import sys
import os
sys.path.append('.')

from src.tools.reservations import create_reservation, update_reservation
from src.tools.guests import create_guest, update_guest

async def test_property_id_handling():
    """Test that property ID is handled correctly for write operations."""
    
    print("🧪 Testing Property ID Handling for Write Operations")
    print("=" * 60)
    
    # Test 1: Create reservation with minimal valid data
    print("\n1. Testing create_reservation with minimal data...")
    reservation_data = {
        "startDate": "2024-06-01",
        "endDate": "2024-06-05",
        "guestFirstName": "Test",
        "guestLastName": "User",
        "guestEmail": "<EMAIL>",
        "adults": 2,
        "roomTypeID": "test-room-type"
    }
    
    try:
        result = await create_reservation(reservation_data)
        print(f"   Result: {result}")
        
        if "error" in result:
            if "You don't have access to property ID" in result["error"]:
                print("   ❌ Still getting property ID access error")
            else:
                print(f"   ⚠️  Different error: {result['error']}")
        else:
            print("   ✅ No property ID access error!")
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test 2: Create guest with minimal valid data
    print("\n2. Testing create_guest with minimal data...")
    guest_data = {
        "guestFirstName": "Test",
        "guestLastName": "Guest",
        "guestEmail": "<EMAIL>"
    }
    
    try:
        result = await create_guest(guest_data)
        print(f"   Result: {result}")
        
        if "error" in result:
            if "You don't have access to property ID" in result["error"]:
                print("   ❌ Still getting property ID access error")
            else:
                print(f"   ⚠️  Different error: {result['error']}")
        else:
            print("   ✅ No property ID access error!")
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    # Test 3: Update reservation (this will fail because reservation doesn't exist, but should not be property ID error)
    print("\n3. Testing update_reservation...")
    update_data = {
        "adults": 3,
        "notes": "Updated by test"
    }
    
    try:
        result = await update_reservation("test-reservation-id", update_data)
        print(f"   Result: {result}")
        
        if "error" in result:
            if "You don't have access to property ID" in result["error"]:
                print("   ❌ Still getting property ID access error")
            else:
                print(f"   ⚠️  Different error (expected): {result['error']}")
        else:
            print("   ✅ No property ID access error!")
            
    except Exception as e:
        print(f"   ❌ Exception: {e}")
    
    print("\n" + "=" * 60)
    print("🏁 Test completed!")
    print("\nIf you see 'Still getting property ID access error', the fix needs more work.")
    print("If you see 'Different error', that's expected - we're testing with fake data.")
    print("If you see 'No property ID access error', the fix is working! 🎉")

if __name__ == "__main__":
    # Check if we have the required environment variables
    if not os.getenv("CLOUDBEDS_API_KEY"):
        print("❌ CLOUDBEDS_API_KEY environment variable not set")
        print("Please create a .env file with your Cloudbeds API credentials")
        sys.exit(1)
    
    if not os.getenv("CLOUDBEDS_PROPERTY_ID"):
        print("❌ CLOUDBEDS_PROPERTY_ID environment variable not set")
        print("Please create a .env file with your Cloudbeds API credentials")
        sys.exit(1)
    
    asyncio.run(test_property_id_handling())
