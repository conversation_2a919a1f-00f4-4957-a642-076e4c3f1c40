"""
Guest tools for Cloudbeds MCP.

This module provides tools for interacting with Cloudbeds guests.
"""

import datetime
from typing import Dict, List, Optional, Any

from src.cloudbeds_client import api_request
from src.config import CLOUDBEDS_PROPERTY_ID

async def get_guest_details(
    guest_id: str,
    property_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Retrieve detailed information for a specific guest.

    Args:
        guest_id (str): Guest ID
        property_id (str, optional): Cloudbeds property ID

    Returns:
        dict: Detailed guest data
    """
    # Request parameters
    params = {
        "guestID": guest_id
    }

    return await api_request("getGuest", params, property_id or CLOUDBEDS_PROPERTY_ID)

async def search_guests(
    search_term: str,
    property_id: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Search for guests by name, email, or phone.

    Args:
        search_term (str): Search term
        property_id (str, optional): Cloudbeds property ID

    Returns:
        list: List of matching guest data
    """
    # Request parameters
    params = {
        "term": search_term
    }

    return await api_request("searchGuests", params, property_id or CLOUDBEDS_PROPERTY_ID)

async def create_guest(
    guest_data: Dict[str, Any],
    property_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Create a new guest in Cloudbeds.

    Args:
        guest_data (dict): Guest data containing:
            - guestFirstName (str): Guest first name
            - guestLastName (str): Guest last name
            - guestEmail (str): Guest email address
            - guestPhone (str, optional): Guest phone number
            - guestAddress (str, optional): Guest address
            - guestCity (str, optional): Guest city
            - guestZip (str, optional): Guest zip code
            - guestCountry (str, optional): Guest country
            - guestGender (str, optional): Guest gender
            - guestBirthdate (str, optional): Guest birthdate in YYYY-MM-DD format
        property_id (str, optional): Cloudbeds property ID

    Returns:
        dict: Created guest data or error information
    """
    # Validate required fields
    required_fields = ['guestFirstName', 'guestLastName', 'guestEmail']
    missing_fields = [field for field in required_fields if field not in guest_data]

    if missing_fields:
        return {
            "error": f"Missing required fields: {', '.join(missing_fields)}",
            "required_fields": required_fields
        }

    # Validate email format (basic validation)
    email = guest_data.get('guestEmail', '')
    if '@' not in email or '.' not in email:
        return {"error": "Invalid email format"}

    # Validate birthdate format if provided
    if 'guestBirthdate' in guest_data:
        try:
            datetime.datetime.strptime(guest_data['guestBirthdate'], '%Y-%m-%d')
        except ValueError:
            return {"error": "Invalid birthdate format. Use YYYY-MM-DD format"}

    return await api_request("postGuest", data=guest_data, property_id=property_id or CLOUDBEDS_PROPERTY_ID, method="POST")

async def update_guest(
    guest_id: str,
    update_data: Dict[str, Any],
    property_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Update an existing guest in Cloudbeds.

    Args:
        guest_id (str): Guest ID
        update_data (dict): Data to update, can include:
            - guestFirstName (str): Guest first name
            - guestLastName (str): Guest last name
            - guestEmail (str): Guest email address
            - guestPhone (str): Guest phone number
            - guestAddress (str): Guest address
            - guestCity (str): Guest city
            - guestZip (str): Guest zip code
            - guestCountry (str): Guest country
            - guestGender (str): Guest gender
            - guestBirthdate (str): Guest birthdate in YYYY-MM-DD format
        property_id (str, optional): Cloudbeds property ID

    Returns:
        dict: Updated guest data or error information
    """
    if not guest_id:
        return {"error": "Guest ID is required"}

    if not update_data:
        return {"error": "Update data is required"}

    # Validate email format if provided
    if 'guestEmail' in update_data:
        email = update_data['guestEmail']
        if '@' not in email or '.' not in email:
            return {"error": "Invalid email format"}

    # Validate birthdate format if provided
    if 'guestBirthdate' in update_data:
        try:
            datetime.datetime.strptime(update_data['guestBirthdate'], '%Y-%m-%d')
        except ValueError:
            return {"error": "Invalid birthdate format. Use YYYY-MM-DD format"}

    # Add guest ID to the update data
    update_data["guestID"] = guest_id

    return await api_request("putGuest", data=update_data, property_id=property_id or CLOUDBEDS_PROPERTY_ID, method="PUT")
