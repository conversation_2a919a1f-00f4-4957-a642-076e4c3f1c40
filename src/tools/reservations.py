"""
Reservation tools for Cloudbeds MCP.

This module provides tools for interacting with Cloudbeds reservations.
"""

import datetime
from typing import Dict, List, Optional, Any

from src.cloudbeds_client import api_request
from src.config import CLOUDBEDS_PROPERTY_ID

async def get_reservations(
    days_back: int = 30,
    property_id: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Retrieve reservations from Cloudbeds API.

    Args:
        days_back (int): Number of days to look back for reservations
        property_id (str, optional): Cloudbeds property ID

    Returns:
        list: List of reservation data
    """
    # Calculate date range
    end_date = datetime.datetime.now() + datetime.timedelta(days=400)  # Include future reservations
    start_date = datetime.datetime.now() - datetime.timedelta(days=days_back)

    # Format dates for API
    start_date_str = start_date.strftime('%Y-%m-%d')
    end_date_str = end_date.strftime('%Y-%m-%d')

    # Request parameters
    params = {
        "checkInFrom": start_date_str,
        "checkInTo": end_date_str,
        "includeGuestInfo": "true",
        "includeRoomInfo": "true"
    }

    return await api_request("getReservations", params, property_id or CLOUDBEDS_PROPERTY_ID)

async def get_reservation_details(
    reservation_id: str,
    property_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Retrieve detailed information for a specific reservation.

    Args:
        reservation_id (str): Reservation ID
        property_id (str, optional): Cloudbeds property ID

    Returns:
        dict: Detailed reservation data
    """
    # Request parameters
    params = {
        "reservationID": reservation_id
    }

    return await api_request("getReservation", params, property_id or CLOUDBEDS_PROPERTY_ID)

async def get_reservation_invoice(
    reservation_id: str,
    property_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Retrieve invoice information for a specific reservation.

    Args:
        reservation_id (str): Reservation ID
        property_id (str, optional): Cloudbeds property ID

    Returns:
        dict: Invoice data
    """
    # Request parameters
    params = {
        "reservationID": reservation_id
    }

    return await api_request("getInvoice", params, property_id or CLOUDBEDS_PROPERTY_ID)

async def create_reservation(
    reservation_data: Dict[str, Any],
    property_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Create a new reservation in Cloudbeds.

    Args:
        reservation_data (dict): Reservation data containing:
            - startDate (str): Check-in date in YYYY-MM-DD format
            - endDate (str): Check-out date in YYYY-MM-DD format
            - guestFirstName (str): Guest first name
            - guestLastName (str): Guest last name
            - guestEmail (str): Guest email
            - adults (int): Number of adults
            - children (int, optional): Number of children
            - roomTypeID (str): Room type ID
            - ratePlanID (str, optional): Rate plan ID
            - source (str, optional): Reservation source
            - notes (str, optional): Special requests or notes
        property_id (str, optional): Cloudbeds property ID

    Returns:
        dict: Created reservation data or error information
    """
    # Validate required fields
    required_fields = ['startDate', 'endDate', 'guestFirstName', 'guestLastName', 'guestEmail', 'adults', 'roomTypeID']
    missing_fields = [field for field in required_fields if field not in reservation_data]

    if missing_fields:
        return {
            "error": f"Missing required fields: {', '.join(missing_fields)}",
            "required_fields": required_fields
        }

    # Validate date format
    try:
        datetime.datetime.strptime(reservation_data['startDate'], '%Y-%m-%d')
        datetime.datetime.strptime(reservation_data['endDate'], '%Y-%m-%d')
    except ValueError:
        return {"error": "Invalid date format. Use YYYY-MM-DD format for startDate and endDate"}

    # Validate check-in is before check-out
    start_date = datetime.datetime.strptime(reservation_data['startDate'], '%Y-%m-%d')
    end_date = datetime.datetime.strptime(reservation_data['endDate'], '%Y-%m-%d')

    if start_date >= end_date:
        return {"error": "Check-in date must be before check-out date"}

    return await api_request("postReservation", data=reservation_data, property_id=property_id or CLOUDBEDS_PROPERTY_ID, method="POST")

async def update_reservation(
    reservation_id: str,
    update_data: Dict[str, Any],
    property_id: Optional[str] = None
) -> Dict[str, Any]:
    """
    Update an existing reservation in Cloudbeds.

    Args:
        reservation_id (str): Reservation ID
        update_data (dict): Data to update, can include:
            - startDate (str): Check-in date in YYYY-MM-DD format
            - endDate (str): Check-out date in YYYY-MM-DD format
            - guestFirstName (str): Guest first name
            - guestLastName (str): Guest last name
            - guestEmail (str): Guest email
            - adults (int): Number of adults
            - children (int): Number of children
            - roomTypeID (str): Room type ID
            - ratePlanID (str): Rate plan ID
            - status (str): Reservation status
            - notes (str): Special requests or notes
        property_id (str, optional): Cloudbeds property ID

    Returns:
        dict: Updated reservation data or error information
    """
    if not reservation_id:
        return {"error": "Reservation ID is required"}

    if not update_data:
        return {"error": "Update data is required"}

    # Validate date formats if provided
    if 'startDate' in update_data:
        try:
            datetime.datetime.strptime(update_data['startDate'], '%Y-%m-%d')
        except ValueError:
            return {"error": "Invalid startDate format. Use YYYY-MM-DD format"}

    if 'endDate' in update_data:
        try:
            datetime.datetime.strptime(update_data['endDate'], '%Y-%m-%d')
        except ValueError:
            return {"error": "Invalid endDate format. Use YYYY-MM-DD format"}

    # Validate check-in is before check-out if both dates are provided
    if 'startDate' in update_data and 'endDate' in update_data:
        start_date = datetime.datetime.strptime(update_data['startDate'], '%Y-%m-%d')
        end_date = datetime.datetime.strptime(update_data['endDate'], '%Y-%m-%d')

        if start_date >= end_date:
            return {"error": "Check-in date must be before check-out date"}

    # Add reservation ID to the update data
    update_data["reservationID"] = reservation_id

    return await api_request("putReservation", data=update_data, property_id=property_id or CLOUDBEDS_PROPERTY_ID, method="PUT")
